<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis 实时数据流 - 现代化仪表板</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;

            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --bg-card: rgba(255, 255, 255, 0.05);
            --bg-glass: rgba(255, 255, 255, 0.1);

            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #94a3b8;

            --border-color: rgba(255, 255, 255, 0.1);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* 现代化头部 */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-light), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.125rem;
            color: var(--text-secondary);
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
        }

        /* 现代化状态栏 */
        .status-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .status-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-icon {
            width: 2rem;
            height: 2rem;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .status-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: baseline;
            gap: 0.5rem;
        }

        .status-trend {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .trend-up { color: var(--success-color); }
        .trend-down { color: var(--danger-color); }
        .trend-stable { color: var(--warning-color); }

        /* 控制面板 */
        .control-panel {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--bg-glass);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-online { background: var(--success-color); }
        .status-offline { background: var(--danger-color); }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .control-button {
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-button:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        /* 主要内容区域 */
        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 2rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .card h3 {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        /* 统计网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            border-color: var(--primary-color);
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .stat-trend {
            font-size: 0.75rem;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        /* 列表容器 */
        .list-container {
            max-height: 500px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }

        .list-container::-webkit-scrollbar {
            width: 6px;
        }

        .list-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .list-container::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .list-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 交易对列表 */
        .symbol-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .symbol-item:hover {
            transform: translateX(4px);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .symbol-rank {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .symbol-info {
            flex: 1;
            margin-left: 1rem;
        }

        .symbol-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .symbol-volume {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .symbol-stats {
            text-align: right;
        }

        .symbol-price {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .symbol-change {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .change-positive { color: var(--success-color); }
        .change-negative { color: var(--danger-color); }

        /* 强制平仓流 - 细长卡片设计 */
        .liquidation-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            animation: slideInRight 0.5s ease-out;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: 3rem;
            min-height: 3rem;
        }

        .liquidation-item.buy {
            background: linear-gradient(135deg, rgba(46, 189, 133, 0.08), rgba(46, 189, 133, 0.03));
        }

        .liquidation-item.sell {
            background: linear-gradient(135deg, rgba(246, 70, 93, 0.08), rgba(246, 70, 93, 0.03));
        }

        .liquidation-item:hover {
            transform: translateX(2px);
            border-color: var(--primary-color);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 条形指示器 */
        .amount-indicator {
            display: flex;
            flex-direction: column;
            gap: 1px;
            margin-right: 1rem;
            width: 20px;
            height: 2.5rem;
            justify-content: center;
        }

        .indicator-bar {
            height: 2px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1px;
            transition: all 0.3s ease;
        }

        .indicator-bar.active-buy {
            background: rgb(46, 189, 133);
            box-shadow: 0 0 4px rgba(46, 189, 133, 0.5);
        }

        .indicator-bar.active-sell {
            background: rgb(246, 70, 93);
            box-shadow: 0 0 4px rgba(246, 70, 93, 0.5);
        }

        /* 指示器动画效果 */
        .indicator-bar.active-buy,
        .indicator-bar.active-sell {
            animation: barGlow 2s ease-in-out infinite alternate;
        }

        @keyframes barGlow {
            from {
                opacity: 0.8;
            }
            to {
                opacity: 1;
            }
        }

        .liquidation-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
        }

        .liquidation-symbol {
            font-weight: 600;
            color: var(--text-primary);
            min-width: 80px;
            font-size: 0.875rem;
        }

        .side-badge {
            padding: 0.125rem 0.5rem;
            border-radius: var(--radius-sm);
            color: white;
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            min-width: 35px;
            text-align: center;
        }

        .side-buy { background: rgb(46, 189, 133); }
        .side-sell { background: rgb(246, 70, 93); }

        .liquidation-center {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 2;
        }

        .liquidation-price {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 0.875rem;
            min-width: 80px;
        }

        .liquidation-quantity {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 0.875rem;
            min-width: 80px;
        }

        .liquidation-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            justify-content: flex-end;
            flex: 1;
        }

        .liquidation-amount {
            font-weight: 700;
            color: var(--text-primary);
            text-align: right;
            min-width: 100px;
            font-size: 0.875rem;
        }

        .liquidation-time {
            font-size: 0.75rem;
            color: var(--text-muted);
            min-width: 60px;
            text-align: right;
        }

        /* 底部信息栏 */
        .footer-info {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 2rem;
            text-align: center;
            margin-top: 2rem;
        }

        .data-flow {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .flow-item {
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .flow-arrow {
            color: var(--secondary-color);
            font-size: 1.25rem;
            font-weight: 600;
        }

        .system-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .metric-item {
            text-align: center;
            padding: 1rem;
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .status-bar {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .control-panel {
                flex-direction: column;
                align-items: stretch;
            }

            .control-group {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Redis 实时数据流</h1>
            <p>现代化仪表板 - 展示 Redis Stream & Pub/Sub + ClickHouse + WebSocket 实时数据处理</p>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-group">
                <div class="status-indicator">
                    <div class="status-dot status-online" id="redisStatus"></div>
                    <span>Redis</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot status-online" id="clickhouseStatus"></div>
                    <span>ClickHouse</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot status-online" id="websocketStatus"></div>
                    <span>WebSocket</span>
                </div>
            </div>

            <div class="control-group">
                <button class="control-button" onclick="toggleUpdates()">
                    <span id="updateToggle">⏸️</span>
                    <span>暂停更新</span>
                </button>
                <button class="control-button" onclick="changeRefreshRate()">
                    <span>🔄</span>
                    <span id="refreshRate">5秒刷新</span>
                </button>
                <button class="control-button" onclick="toggleTheme()">
                    <span>🌙</span>
                    <span>深色模式</span>
                </button>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">总订单数</div>
                    <div class="status-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">📊</div>
                </div>
                <div class="status-value">
                    <span id="totalOrders">-</span>
                </div>
                <div class="status-trend trend-up" id="ordersChange">
                    <span>↗️</span>
                    <span>+5.2% vs 上小时</span>
                </div>
            </div>
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">平均数量</div>
                    <div class="status-icon" style="background: linear-gradient(135deg, var(--success-color), #34d399);">⚖️</div>
                </div>
                <div class="status-value">
                    <span id="avgQuantity">-</span>
                </div>
                <div class="status-trend trend-stable" id="quantityChange">
                    <span>➡️</span>
                    <span>-1.1% vs 上小时</span>
                </div>
            </div>
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">交易对数</div>
                    <div class="status-icon" style="background: linear-gradient(135deg, var(--warning-color), #fbbf24);">🎯</div>
                </div>
                <div class="status-value">
                    <span id="uniqueSymbols">-</span>
                </div>
                <div class="status-trend trend-up" id="symbolsChange">
                    <span>↗️</span>
                    <span>+3.8% vs 上小时</span>
                </div>
            </div>
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">更新时间</div>
                    <div class="status-icon" style="background: linear-gradient(135deg, var(--info-color), #60a5fa);">⏰</div>
                </div>
                <div class="status-value" id="updateTime">-</div>
                <div class="status-trend">
                    <span>延迟: <span id="latency">23ms</span></span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-grid">
            <!-- 实时统计卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>
                        <span>📊</span>
                        <span>实时统计</span>
                    </h3>
                    <div class="card-actions">
                        <button class="action-btn" onclick="showChart('stats')">📈</button>
                        <button class="action-btn" onclick="exportData('stats')">📤</button>
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">
                            <span id="sellOrders">-</span>
                            <span class="trend-down">↘️</span>
                        </div>
                        <div class="stat-label">卖单数量</div>
                        <div class="stat-trend">
                            <span>占比: <span id="sellRatio">74%</span></span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">
                            <span id="buyOrders">-</span>
                            <span class="trend-up">↗️</span>
                        </div>
                        <div class="stat-label">买单数量</div>
                        <div class="stat-trend">
                            <span>占比: <span id="buyRatio">26%</span></span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">
                            <span id="totalQuantity">-</span>
                            <span class="trend-up">↗️</span>
                        </div>
                        <div class="stat-label">总交易量</div>
                        <div class="stat-trend">
                            <span>24h: +12.5%</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">CH</div>
                        <div class="stat-label">数据源</div>
                        <div class="stat-trend">
                            <span>响应: <span id="dbLatency">45ms</span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门交易对卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>
                        <span>🏆</span>
                        <span>热门交易对</span>
                    </h3>
                    <div class="card-actions">
                        <button class="action-btn" onclick="showMore('symbols')">📋</button>
                        <button class="action-btn" onclick="refreshSymbols()">🔄</button>
                    </div>
                </div>
                <div class="list-container" id="symbolList">
                    <div style="text-align: center; color: var(--text-muted); padding: 2rem;">
                        等待数据加载...
                    </div>
                </div>
            </div>

            <!-- 实时强制平仓卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>
                        <span>🔥</span>
                        <span>实时强制平仓</span>
                    </h3>
                    <div class="card-actions">
                        <button class="action-btn" onclick="pauseStream()">
                            <span id="streamToggle">⏸️</span>
                        </button>
                        <button class="action-btn" onclick="clearStream()">🗑️</button>
                    </div>
                </div>
                <div class="list-container" id="liquidationList">
                    <div style="text-align: center; color: var(--text-muted); padding: 2rem;">
                        等待实时数据...
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部信息栏 -->
        <div class="footer-info">
            <div class="data-flow">
                <div class="flow-item">ClickHouse</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">Redis Stream</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">Pub/Sub</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">WebSocket</div>
                <div class="flow-arrow">→</div>
                <div class="flow-item">前端页面</div>
            </div>
            
            <div class="system-metrics">
                <div class="metric-item">
                    <div class="metric-value" id="streamLength">-</div>
                    <div class="metric-label">Stream队列长度</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="pubsubClients">-</div>
                    <div class="metric-label">Pub/Sub订阅者</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="wsConnections">-</div>
                    <div class="metric-label">WebSocket连接</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="totalLatency">-</div>
                    <div class="metric-label">端到端延迟</div>
                </div>
            </div>
            
            <div style="margin-top: 15px; opacity: 0.8;">
                最后更新: <span id="lastUpdate">-</span> | 
                Redis版本: 7.x | 
                处理记录: <span id="processedCount">405万+</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let socket;
        let isUpdating = true;
        let refreshInterval = 5000;
        let streamPaused = false;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initWebSocket();
            console.log('🚀 增强版Redis演示页面加载完成');
        });
        
        // WebSocket初始化
        function initWebSocket() {
            socket = io();
            
            socket.on('connect', () => {
                updateConnectionStatus('websocket', true);
                console.log('🔌 WebSocket连接成功');
            });
            
            socket.on('disconnect', () => {
                updateConnectionStatus('websocket', false);
                console.log('❌ WebSocket连接断开');
            });
            
            socket.on('realtime-data', (data) => {
                if (isUpdating) {
                    console.log('📊 收到实时数据:', data);
                    updateUI(data);
                }
            });
        }
        
        // 更新连接状态
        function updateConnectionStatus(service, isOnline) {
            const statusElement = document.getElementById(service + 'Status');
            if (statusElement) {
                statusElement.className = `status-dot ${isOnline ? 'status-online' : 'status-offline'}`;
            }
        }
        
        // 更新UI (简化版本，完整版本会更复杂)
        function updateUI(data) {
            const { stats, topSymbols, latestLiquidations } = data;
            
            // 更新状态栏
            document.getElementById('totalOrders').textContent = formatNumber(stats.total_orders || 0);
            document.getElementById('avgQuantity').textContent = formatNumber(stats.avg_quantity || 0);
            document.getElementById('uniqueSymbols').textContent = stats.unique_symbols || 0;
            document.getElementById('updateTime').textContent = new Date().toLocaleTimeString();
            
            // 更新统计数据
            document.getElementById('sellOrders').textContent = formatNumber(stats.sell_orders || 0);
            document.getElementById('buyOrders').textContent = formatNumber(stats.buy_orders || 0);
            document.getElementById('totalQuantity').textContent = formatNumber(stats.total_quantity || 0);
            
            // 计算比例
            const total = (stats.sell_orders || 0) + (stats.buy_orders || 0);
            if (total > 0) {
                document.getElementById('sellRatio').textContent = Math.round((stats.sell_orders / total) * 100) + '%';
                document.getElementById('buyRatio').textContent = Math.round((stats.buy_orders / total) * 100) + '%';
            }
            
            // 更新交易对列表
            updateSymbolList(topSymbols || []);
            
            // 更新强制平仓列表
            if (!streamPaused) {
                updateLiquidationList(latestLiquidations || []);
            }
            
            // 更新最后更新时间
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
            
            // 模拟系统指标
            document.getElementById('streamLength').textContent = Math.floor(Math.random() * 1000) + 500;
            document.getElementById('pubsubClients').textContent = Math.floor(Math.random() * 10) + 1;
            document.getElementById('wsConnections').textContent = Math.floor(Math.random() * 5) + 1;
            document.getElementById('totalLatency').textContent = Math.floor(Math.random() * 50) + 20 + 'ms';
            document.getElementById('latency').textContent = Math.floor(Math.random() * 30) + 15 + 'ms';
            document.getElementById('dbLatency').textContent = Math.floor(Math.random() * 80) + 30 + 'ms';
        }
        
        // 更新交易对列表 (增强版)
        function updateSymbolList(symbols) {
            const container = document.getElementById('symbolList');
            
            if (symbols.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无数据</div>';
                return;
            }
            
            container.innerHTML = symbols.map((symbol, index) => `
                <div class="symbol-item" onclick="showSymbolDetails('${symbol.symbol}')">
                    <div class="symbol-rank">${index + 1}</div>
                    <div class="symbol-info">
                        <div class="symbol-name">${symbol.symbol}</div>
                        <div class="symbol-volume">交易量: ${formatNumber(symbol.total_quantity)}</div>
                    </div>
                    <div class="symbol-stats">
                        <div class="symbol-price">$${formatNumber(symbol.avg_price)}</div>
                        <div class="symbol-change change-positive">+${Math.random() * 5 + 1}%</div>
                    </div>
                </div>
            `).join('');
        }
        
        // 根据爆仓金额获取指示器档位 (1-10档)
        function getAmountLevel(amount) {
            if (amount < 1000) return 1;
            if (amount < 5000) return 2;
            if (amount < 10000) return 3;
            if (amount < 50000) return 4;
            if (amount < 100000) return 5;
            if (amount < 500000) return 6;
            if (amount < 1000000) return 7;
            if (amount < 5000000) return 8;
            if (amount < 10000000) return 9;
            return 10; // >= 10,000,000
        }

        // 生成条形指示器HTML
        function generateAmountIndicator(level, side) {
            const activeClass = side.toLowerCase() === 'buy' ? 'active-buy' : 'active-sell';
            let barsHtml = '';

            for (let i = 1; i <= 10; i++) {
                const isActive = i <= level;
                barsHtml += `<div class="indicator-bar ${isActive ? activeClass : ''}"></div>`;
            }

            return `<div class="amount-indicator">${barsHtml}</div>`;
        }

        // 更新强制平仓列表 (条形指示器版)
        function updateLiquidationList(liquidations) {
            const container = document.getElementById('liquidationList');

            if (liquidations.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: var(--text-muted); padding: 2rem;">暂无数据</div>';
                return;
            }

            container.innerHTML = liquidations.map(liquidation => {
                const liquidationAmount = liquidation.quantity * liquidation.price;
                const amountLevel = getAmountLevel(liquidationAmount);
                const indicator = generateAmountIndicator(amountLevel, liquidation.side);

                return `
                    <div class="liquidation-item ${liquidation.side.toLowerCase()}">
                        ${indicator}
                        <div class="liquidation-left">
                            <div class="liquidation-symbol">${liquidation.symbol}</div>
                            <span class="side-badge side-${liquidation.side.toLowerCase()}">
                                ${liquidation.side}
                            </span>
                        </div>
                        <div class="liquidation-center">
                            <div class="liquidation-price">$${formatNumber(liquidation.price)}</div>
                            <div class="liquidation-quantity">${formatNumber(liquidation.quantity)}</div>
                        </div>
                        <div class="liquidation-right">
                            <div class="liquidation-amount">
                                $${formatNumber(liquidationAmount)}
                            </div>
                            <div class="liquidation-time">
                                ${getRelativeTime(liquidation.timestamp)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 控制函数
        function toggleUpdates() {
            isUpdating = !isUpdating;
            const toggleBtn = document.getElementById('updateToggle');
            const toggleText = toggleBtn.nextElementSibling;
            if (isUpdating) {
                toggleBtn.textContent = '⏸️';
                toggleText.textContent = '暂停更新';
            } else {
                toggleBtn.textContent = '▶️';
                toggleText.textContent = '继续更新';
            }
        }

        function changeRefreshRate() {
            const rates = [1000, 5000, 10000];
            const labels = ['1秒刷新', '5秒刷新', '10秒刷新'];
            const currentIndex = rates.indexOf(refreshInterval);
            const nextIndex = (currentIndex + 1) % rates.length;
            refreshInterval = rates[nextIndex];
            document.getElementById('refreshRate').textContent = labels[nextIndex];
        }

        function pauseStream() {
            streamPaused = !streamPaused;
            const toggleBtn = document.getElementById('streamToggle');
            toggleBtn.textContent = streamPaused ? '▶️' : '⏸️';
        }

        function toggleTheme() {
            // 主题切换逻辑 - 可以在这里添加亮色主题
            console.log('切换主题');
        }
        
        // 工具函数
        function formatNumber(num) {
            if (num === null || num === undefined) return '-';
            const numValue = parseFloat(num);

            if (numValue >= 1000000000) {
                return (numValue / 1000000000).toFixed(1) + 'B';
            } else if (numValue >= 1000000) {
                return (numValue / 1000000).toFixed(1) + 'M';
            } else if (numValue >= 1000) {
                return (numValue / 1000).toFixed(1) + 'K';
            } else if (numValue >= 1) {
                return numValue.toFixed(2);
            } else {
                return numValue.toFixed(4);
            }
        }
        
        function getRelativeTime(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diff = Math.floor((now - time) / 1000);
            
            if (diff < 60) return `${diff}秒前`;
            if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
            return time.toLocaleTimeString();
        }
        
        // 占位函数
        function showChart(type) { console.log('显示图表:', type); }
        function exportData(type) { console.log('导出数据:', type); }
        function showMore(type) { console.log('显示更多:', type); }
        function refreshSymbols() { console.log('刷新交易对'); }
        function clearStream() { console.log('清空流'); }
        function showSymbolDetails(symbol) { console.log('显示详情:', symbol); }
    </script>
</body>
</html>
